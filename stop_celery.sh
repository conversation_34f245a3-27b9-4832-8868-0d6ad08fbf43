#!/bin/bash

# Stop Celery Worker and Beat Scheduler

echo "Stopping Celery Worker..."
if [ -f celery_worker.pid ]; then
    pkill -F celery_worker.pid
    rm celery_worker.pid
    echo "Celery Worker stopped."
else
    echo "Celery Worker PID file not found. Trying to kill by process name..."
    pkill -f "celery.*worker"
fi

echo "Stopping Celery Beat Scheduler..."
if [ -f celery_beat.pid ]; then
    pkill -F celery_beat.pid
    rm celery_beat.pid
    echo "Celery Beat Scheduler stopped."
else
    echo "Celery Beat PID file not found. Trying to kill by process name..."
    pkill -f "celery.*beat"
fi

echo "Celery services stopped!"
