CREATE TABLE IF NOT EXISTS userbot_scheduledmessage (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    text TEXT NOT NULL,
    interval_minutes INTEGER NOT NULL CHECK (interval_minutes > 0),
    is_active B<PERSON><PERSON>EAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    last_sent_at TIMESTAMP WITH TIME ZONE,
    sent_count INTEGER NOT NULL DEFAULT 0 CHECK (sent_count >= 0),
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    error_message TEXT,
    celery_task_id VARCHAR(255)
);
