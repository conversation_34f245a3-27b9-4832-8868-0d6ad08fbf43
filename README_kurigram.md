# 🚖 Kurigram Session Creator

Kurigram API bilan Telegram sessiya yaratish uchun mo'ljallangan Python script.

## 📋 Tavsif

Bu script Kurigram (Pyrogram fork) kutubxonasidan foydalanib, Telegram API bilan sessiya yaratadi. Script SMS orqali tasdiqlash jarayonini avtomatik boshqaradi va foydalanuvchi-do'st interfeys taqdim etadi.

## 🔧 Xususiyatlar

- ✅ **API ID va API Hash** qabul qilish
- ✅ **Telefon raqam** kiritish (+998901234567 formatida)
- ✅ **SMS kod** orqali tasdiqlash
- ✅ **Ikki bosqichli autentifikatsiya** qo'llab-quvvatlash
- ✅ **Session fayli** yaratish
- ✅ **Xatoliklarni boshqarish**
- ✅ **Logging tizimi**
- ✅ **Foydalanuvchi-do'st interfeys**

## 📦 O'rnatish

### 1. <PERSON><PERSON>ram kutubxonasini o'rnatish

```bash
pip install kurigram
```

### 2. Yoki requirements.txt orqali

```bash
pip install -r requirements.txt
```

## 🚀 Foydalanish

### Asosiy ishlatish

```bash
python kurigram.py
```

Script ishga tushganda sizdan ketma-ket quyidagilar so'raladi:

1. **API ID** - Telegram API ID (faqat raqamlar)
2. **API Hash** - Telegram API Hash
3. **Telefon raqam** - To'liq formatda (+998901234567)
4. **Session nomi** - Ixtiyoriy (default: session_telefon_raqam)
5. **SMS kod** - Telegram tomonidan yuborilgan kod

### API ID va API Hash olish

1. [my.telegram.org](https://my.telegram.org) saytiga kiring
2. Telefon raqamingiz bilan kirish
3. "API development tools" bo'limiga o'ting
4. Yangi dastur yarating
5. API ID va API Hash ni oling

## 📁 Chiqish

Muvaffaqiyatli yaratilgan sessiya fayli loyiha papkasida saqlanadi:
- Fayl nomi: `{session_nomi}.session`
- Format: SQLite database

## 🔐 Xavfsizlik

- Session fayllari shaxsiy ma'lumotlarni o'z ichiga oladi
- Ularni boshqalar bilan baham ko'rmang
- Git repository'ga qo'shmang (.gitignore ga qo'shing)

## ⚠️ Talablar

- **Python 3.8+**
- **Kurigram kutubxonasi**
- **Internet aloqasi**
- **Telegram akkaunt**

## 🛠 Misol

```python
from pyrogram import Client

# Session yaratilgandan keyin
app = Client("my_session")

async def main():
    async with app:
        me = await app.get_me()
        print(f"Salom, {me.first_name}!")

app.run(main())
```

## 📝 Litsenziya

Bu loyiha MIT litsenziyasi ostida tarqatiladi.

## 🤝 Hissa qo'shish

1. Repository'ni fork qiling
2. Yangi branch yarating
3. O'zgarishlarni commit qiling
4. Pull request yuboring

## 📞 Yordam

Agar muammolar yuzaga kelsa:

1. [Kurigram dokumentatsiyasi](https://docs.kurigram.live)
2. [GitHub Issues](https://github.com/KurimuzonAkuma/pyrogram/issues)
3. [Telegram kanal](https://t.me/kurigram_news)

## 🔄 Yangilanishlar

- **v1.0.0** - Asosiy funksiyalar
- Kurigram API bilan integratsiya
- SMS va ikki bosqichli autentifikatsiya
- Xatoliklarni boshqarish

---

**Eslatma:** Bu script faqat o'quv maqsadlari uchun yaratilgan. Telegram API dan foydalanishda ularning shartlariga rioya qiling.
