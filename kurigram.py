#!/usr/bin/env python3
"""
🚖 KURIGRAM - TELEGRAM SESSION CREATOR 🚖

Kurigram API bilan Telegram sessiya yaratish uchun mo'ljallangan script.
Django admin panel bilan integratsiya qilingan.

Muallif: AI Assistant
Versiya: 2.0.0
Sana: 2025-01-26
"""
import os
import sys
import json
import asyncio
import logging
import django
from asgiref.sync import sync_to_async

# Django sozlamalari
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from pyrogram import Client
from pyrogram.errors import (
    SessionPasswordNeeded,
    PhoneCodeInvalid,
    PhoneNumberInvalid
)

# Django modellarini import qilish
from userbot.models import Session as SessionModel
from backend.settings import STATIC_ROOT


logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class KurigramSessionCreator:
    """Kurigram sessiya yaratuvchi klass"""

    def __init__(self, admin_mode=True):
        self.client = None
        self.api_id = None
        self.api_hash = None
        self.phone = None
        self.session_name = None
        self.admin_mode = admin_mode  # Doim admin mode
        self.two_fa_password = None
        self.sessions_file = os.path.join(STATIC_ROOT, "sessions.json")
    
    def get_user_input(self):
        """Foydalanuvchidan kerakli ma'lumotlarni olish"""
        print("=" * 50)
        print("🚖 KURIGRAM - TELEGRAM SESSION CREATOR 🚖")
        print("=" * 50)
        
        try:
            # API ID ni olish
            while True:
                api_id_input = input("📱 API ID ni kiriting: ").strip()
                if api_id_input.isdigit():
                    self.api_id = int(api_id_input)
                    break
                else:
                    print("❌ API ID faqat raqamlardan iborat bo'lishi kerak!")
            
            # API Hash ni olish
            while True:
                self.api_hash = input("🔑 API Hash ni kiriting: ").strip()
                if self.api_hash:
                    break
                else:
                    print("❌ API Hash bo'sh bo'lishi mumkin emas!")
            
            # Telefon raqamni olish
            while True:
                self.phone = input("📞 Telefon raqamni kiriting (+998901234567): ").strip()
                if self.phone.startswith('+') and len(self.phone) >= 10:
                    break
                else:
                    print("❌ Telefon raqam + belgisi bilan boshlanishi va kamida 10 ta belgidan iborat bo'lishi kerak!")
            
            # Session nomini olish
            session_default = f"session_{self.phone.replace('+', '').replace(' ', '')}"
            session_input = input(f"💾 Session nomi ({session_default}): ").strip()
            self.session_name = session_input if session_input else session_default

            # Ikki bosqichli autentifikatsiya parolini olish (ixtiyoriy)
            print("\n🔐 Ikki bosqichli autentifikatsiya (2FA)")
            print("Agar sizda 2FA yoqilgan bo'lsa, parolni kiriting. Aks holda bo'sh qoldiring.")
            self.two_fa_password = input("🔑 2FA paroli (ixtiyoriy): ").strip() or None

            print("\n✅ Ma'lumotlar muvaffaqiyatli qabul qilindi!")
            return True
            
        except KeyboardInterrupt:
            print("\n\n❌ Jarayon bekor qilindi!")
            return False
        except Exception as e:
            logger.error(f"Ma'lumot olishda xatolik: {e}")
            print(f"❌ Xatolik: {e}")
            return False

    async def save_to_database(self, user_id=None, two_fa_password=None):
        """Ma'lumotlarni Django ma'lumotlar bazasiga saqlash"""
        try:
            # Session obyektini yaratish yoki yangilash (async)
            session_obj, created = await sync_to_async(SessionModel.objects.get_or_create)(
                session_name=self.session_name,
                defaults={
                    'api_id': str(self.api_id),
                    'api_hash': self.api_hash,
                    'number': self.phone.replace('+', ''),
                    'user_id': user_id,
                    'two_fa_password': two_fa_password,
                    'is_active': True
                }
            )

            if not created:
                # Agar mavjud bo'lsa, yangilash
                session_obj.api_id = str(self.api_id)
                session_obj.api_hash = self.api_hash
                session_obj.number = self.phone.replace('+', '')
                session_obj.user_id = user_id
                session_obj.two_fa_password = two_fa_password
                session_obj.is_active = True
                await sync_to_async(session_obj.save)()

            logger.info(f"Session ma'lumotlari ma'lumotlar bazasiga saqlandi: {self.session_name}")
            return True

        except Exception as e:
            logger.error(f"Ma'lumotlar bazasiga saqlashda xatolik: {e}")
            return False

    def save_to_sessions_json(self, user_id=None):
        """Ma'lumotlarni sessions.json fayliga saqlash"""
        try:
            # Mavjud ma'lumotlarni o'qish
            if os.path.exists(self.sessions_file):
                with open(self.sessions_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            else:
                data = []

            # Yangi session ma'lumotlari
            session_data = {
                "api_id": str(self.api_id),
                "api_hash": self.api_hash,
                "session_name": self.session_name,
                "number": self.phone.replace('+', ''),
                "user_id": user_id,
                "is_active": True
            }

            # Mavjud session'ni topish va yangilash yoki yangi qo'shish
            existing_index = None
            for i, item in enumerate(data):
                if item.get('session_name') == self.session_name:
                    existing_index = i
                    break

            if existing_index is not None:
                data[existing_index] = session_data
            else:
                data.append(session_data)

            # Faylga yozish
            with open(self.sessions_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=4, ensure_ascii=False)

            logger.info(f"Session ma'lumotlari sessions.json ga saqlandi: {self.session_name}")
            return True

        except Exception as e:
            logger.error(f"sessions.json ga saqlashda xatolik: {e}")
            return False

    async def create_auto_folder_if_not_exists(self):
        """Auto nomli folder yaratish (agar mavjud bo'lmasa)"""
        try:
            print("\n📁 Auto folder tekshirilmoqda...")

            # Mavjud folderlarni olish
            folders = await self.client.get_folders()

            # Auto nomli folder mavjudligini tekshirish
            auto_folder_exists = False
            for folder in folders:
                if folder.name == "Auto":
                    auto_folder_exists = True
                    print("✅ Auto folder allaqachon mavjud!")
                    break

            # Agar Auto folder mavjud bo'lmasa, yaratish
            if not auto_folder_exists:
                print("📂 Auto folder yaratilmoqda...")
                # "me" (o'zini) included_chats ga qo'shish
                folder_id = await self.client.create_folder(
                    name="Auto",
                    included_chats=["me"]  # O'zini qo'shish
                )
                print(f"✅ Auto folder muvaffaqiyatli yaratildi! (ID: {folder_id})")
                logger.info(f"Auto folder yaratildi: {folder_id}")

            return True

        except Exception as e:
            logger.error(f"Auto folder yaratishda xatolik: {e}")
            print(f"❌ Auto folder yaratishda xatolik: {e}")
            return False
    
    async def create_session(self):
        """Kurigram sessiyasini yaratish"""
        password = None  # Ikki bosqichli autentifikatsiya paroli

        try:
            print(f"\n🔄 '{self.session_name}' sessiyasi yaratilmoqda...")

            # Kurigram client yaratish
            self.client = Client(
                name=self.session_name,
                api_id=self.api_id,
                api_hash=self.api_hash
            )

            # Clientni ulanish
            await self.client.connect()

            # Telefon raqamga kod yuborish
            print(f"📤 {self.phone} raqamiga SMS yuborilmoqda...")
            sent_code = await self.client.send_code(self.phone)

            # SMS kodini olish
            while True:
                try:
                    code = input("📨 SMS orqali kelgan kodni kiriting: ").strip()
                    if not code:
                        print("❌ Kod bo'sh bo'lishi mumkin emas!")
                        continue

                    # Telefon raqam bilan kirish
                    await self.client.sign_in(self.phone, sent_code.phone_code_hash, code)
                    break

                except PhoneCodeInvalid:
                    print("❌ Noto'g'ri kod! Qaytadan urinib ko'ring.")
                    continue
                except SessionPasswordNeeded:
                    # Ikki bosqichli autentifikatsiya
                    print("🔐 Ikki bosqichli autentifikatsiya kerak!")

                    # Agar oldindan parol kiritilgan bo'lsa, uni ishlatish
                    if self.two_fa_password:
                        password = self.two_fa_password
                        print("🔑 Oldindan kiritilgan parol ishlatilmoqda...")
                    else:
                        password = input("🔑 Parolni kiriting: ").strip()

                    try:
                        await self.client.check_password(password)
                        break
                    except Exception as e:
                        print(f"❌ Parol xato: {e}")
                        # Agar oldindan kiritilgan parol xato bo'lsa, qaytadan so'rash
                        if self.two_fa_password:
                            print("🔄 Oldindan kiritilgan parol xato. Qaytadan kiriting:")
                            self.two_fa_password = None
                        continue
                except Exception as e:
                    logger.error(f"Kirish jarayonida xatolik: {e}")
                    print(f"❌ Xatolik yuz berdi: {e}")
                    return False

            # Foydalanuvchi ma'lumotlarini olish
            me = await self.client.get_me()
            user_id = me.id

            print(f"\n✅ Sessiya muvaffaqiyatli yaratildi!")
            print(f"👤 Foydalanuvchi: {me.first_name} {me.last_name or ''}")
            print(f"📱 Telefon: {self.phone}")
            print(f"🆔 ID: {user_id}")
            print(f"💾 Session fayli: {self.session_name}.session")

            # Ma'lumotlar bazasiga saqlash (har doim)
            print("\n💾 Ma'lumotlar bazasiga saqlanmoqda...")
            # Ma'lumotlar bazasiga saqlash
            if await self.save_to_database(user_id=user_id, two_fa_password=password):
                print("✅ Ma'lumotlar bazasiga muvaffaqiyatli saqlandi!")
            else:
                print("❌ Ma'lumotlar bazasiga saqlashda xatolik!")

            # sessions.json ga saqlash
            if self.save_to_sessions_json(user_id=user_id):
                print("✅ sessions.json ga muvaffaqiyatli saqlandi!")
            else:
                print("❌ sessions.json ga saqlashda xatolik!")

            # Auto folder yaratish
            await self.create_auto_folder_if_not_exists()

            return True

        except PhoneNumberInvalid:
            print("❌ Telefon raqam noto'g'ri formatda!")
            return False
        except Exception as e:
            logger.error(f"Sessiya yaratishda xatolik: {e}")
            print(f"❌ Sessiya yaratishda xatolik: {e}")
            return False
        finally:
            if self.client:
                await self.client.disconnect()
    
    async def run(self):
        """Asosiy ishga tushirish funksiyasi"""
        try:
            # Foydalanuvchi ma'lumotlarini olish
            if not self.get_user_input():
                return False
            
            # Sessiya yaratish
            success = await self.create_session()
            
            if success:
                print("\n🎉 Jarayon muvaffaqiyatli yakunlandi!")
                print("📋 Session ma'lumotlari Django admin panelida ko'rinadi.")
                print("🌐 Admin panel: http://localhost:8000/admin/")
                print(f"📁 Session fayli: {os.path.abspath(self.session_name + '.session')}")
            else:
                print("\n❌ Sessiya yaratishda muammo yuz berdi!")
            
            return success
            
        except KeyboardInterrupt:
            print("\n\n❌ Jarayon foydalanuvchi tomonidan bekor qilindi!")
            return False
        except Exception as e:
            logger.error(f"Umumiy xatolik: {e}")
            print(f"❌ Kutilmagan xatolik: {e}")
            return False


async def main():
    """Asosiy funksiya"""
    # Doim admin mode
    creator = KurigramSessionCreator(admin_mode=True)

    print("🔧 Admin rejimi - ma'lumotlar Django bazasiga saqlanadi")
    await creator.run()


def create_session_for_admin(api_id, api_hash, phone, session_name, two_fa_password=None):
    """Admin panel uchun sessiya yaratish funksiyasi"""
    try:
        creator = KurigramSessionCreator(admin_mode=True)
        creator.api_id = int(api_id)
        creator.api_hash = api_hash
        creator.phone = phone
        creator.session_name = session_name

        # Sessiya yaratish
        success = asyncio.run(creator.create_session())
        return success

    except Exception as e:
        logger.error(f"Admin sessiya yaratishda xatolik: {e}")
        return False


if __name__ == "__main__":
    try:
        # Python versiyasini tekshirish
        if sys.version_info < (3, 8):
            print("❌ Python 3.8 yoki undan yuqori versiya kerak!")
            sys.exit(1)

        # Kurigram kutubxonasini tekshirish
        try:
            import pyrogram
        except ImportError:
            print("❌ Kurigram kutubxonasi o'rnatilmagan!")
            print("📦 O'rnatish uchun: pip install kurigram")
            sys.exit(1)

        # Asosiy funksiyani ishga tushirish
        asyncio.run(main())

    except KeyboardInterrupt:
        print("\n\n👋 Xayr!")
    except Exception as e:
        logger.error(f"Dastur ishga tushirishda xatolik: {e}")
        print(f"❌ Dastur ishga tushirishda xatolik: {e}")
        sys.exit(1)
