from asgiref.sync import sync_to_async

from django.db import models


ADMIN = 7713527532


class Session(models.Model):
    """
    Model to store session data.
    """
    api_id = models.CharField(max_length=255, unique=True)
    api_hash = models.CharField(max_length=255, unique=True)
    session_name = models.CharField(max_length=255, unique=True)
    number = models.CharField(max_length=15, unique=True)
    user_id = models.BigIntegerField(null=True, blank=True, help_text="Telegram user ID")
    two_fa_password = models.Char<PERSON>ield(max_length=255, null=True, blank=True)
    is_active = models.BooleanField(default=False)

    def __str__(self):
        return str(self.session_name)

    @classmethod
    def get_by_session_name(cls, session_name: str):
        """
        Get session by session name.
        """
        return cls.objects.get(session_name=session_name)

    def mark_as_active(self):
        """
        Mark the session as active.
        """
        self.is_active = True
        self.save()

    def save(self, *args, **kwargs):
        """
        Save the session.
        """
        self.session_name = self.session_name.lower()
        self.number = self.number.replace("+", "")
        super().save(*args, **kwargs)


class TelegramSession(models.Model):
    """
    Django session.
    """
    session_name = models.CharField(max_length=255, unique=True)
    session_data = models.BinaryField(null=True, blank=True)

    def __str__(self):
        return self.session_name


class BotUser(models.Model):
    """
    Model to store bot users data.
    """
    user_id = models.BigIntegerField(unique=True)
    username = models.CharField(max_length=255, null=True, blank=True)
    first_name = models.CharField(max_length=255, null=True, blank=True)
    last_name = models.CharField(max_length=255, null=True, blank=True)
    language_code = models.CharField(max_length=10, null=True, blank=True)
    is_bot = models.BooleanField(default=False)
    is_premium = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.first_name} ({self.user_id})"

    class Meta:
        verbose_name = "Bot User"
        verbose_name_plural = "Bot Users"


class ScheduledMessage(models.Model):
    """
    Model to store scheduled messages for auto sending
    """
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]

    user_id = models.BigIntegerField(help_text="Telegram user ID")
    text = models.TextField(help_text="Message text to send")
    interval_minutes = models.PositiveIntegerField(help_text="Interval in minutes between sends")
    is_active = models.BooleanField(default=True, help_text="Whether the scheduled message is active")

    # Tracking fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_sent_at = models.DateTimeField(null=True, blank=True)
    sent_count = models.PositiveIntegerField(default=0)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    error_message = models.TextField(null=True, blank=True)

    # Celery task tracking
    celery_task_id = models.CharField(max_length=255, null=True, blank=True)

    def __str__(self):
        return f"Scheduled message for {self.user_id} - {self.interval_minutes}min"

    @property
    def session(self):
        """Get the associated session"""
        try:
            return Session.objects.get(user_id=self.user_id, is_active=True)
        except Session.DoesNotExist:
            return None

    class Meta:
        verbose_name = "Scheduled Message"
        verbose_name_plural = "Scheduled Messages"
        ordering = ['-created_at']
