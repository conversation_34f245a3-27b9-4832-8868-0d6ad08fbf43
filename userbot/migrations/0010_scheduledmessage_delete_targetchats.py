# Generated by Django 5.1.5 on 2025-07-26 05:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('userbot', '0009_add_user_id_to_session'),
    ]

    operations = [
        migrations.CreateModel(
            name='ScheduledMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.BigIntegerField(help_text='Telegram user ID')),
                ('text', models.TextField(help_text='Message text to send')),
                ('interval_minutes', models.PositiveIntegerField(help_text='Interval in minutes between sends')),
                ('is_active', models.<PERSON><PERSON>anField(default=True, help_text='Whether the scheduled message is active')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_sent_at', models.DateTimeField(blank=True, null=True)),
                ('sent_count', models.PositiveIntegerField(default=0)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('sent', 'Sent'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('celery_task_id', models.CharField(blank=True, max_length=255, null=True)),
            ],
            options={
                'verbose_name': 'Scheduled Message',
                'verbose_name_plural': 'Scheduled Messages',
                'ordering': ['-created_at'],
            },
        ),
        migrations.DeleteModel(
            name='TargetChats',
        ),
    ]
